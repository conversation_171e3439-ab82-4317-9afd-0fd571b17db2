import requests
from collections import defaultdict
from datetime import datetime, timedelta



INSTANCE = 'mastodon.social'  

# for post count with date
def get_date_wise_post_counts(account_id,access_token):
    url = f"https://{INSTANCE}/api/v1/accounts/{account_id}/statuses"
    headers = {"Authorization": f"Bearer {access_token}"}
    params = {"limit": 100}
    date_counts = defaultdict(int)

    while url:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        statuses = response.json()

        if not statuses:
            break

        for status in statuses:
            created_at = status['created_at']
            date = datetime.fromisoformat(created_at.rstrip("Z")).date()
            date_counts[date] += 1

        # Check for pagination
        if 'next' in response.links:
            url = response.links['next']['url']
            params = None 
        else:
            break

    if date_counts:
        return True,date_counts
    else:
        return False,''



# # for like count with date 
def get_date_wise_like_counts(account_id,access_token):
    url = f"https://{INSTANCE}/api/v1/accounts/{account_id}/statuses"
    headers = {"Authorization": f"Bearer {access_token}"}
    params = {"limit": 100}
    like_counts = defaultdict(int)

    while url:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        statuses = response.json()

        if not statuses:
            break

        for status in statuses:
            created_at = status['created_at']
            date = datetime.fromisoformat(created_at.rstrip("Z")).date()
            like_counts[date] += status.get('favourites_count', 0)

        if 'next' in response.links:
            url = response.links['next']['url']
            params = None
        else:
            break

    if like_counts:
        return True,like_counts
    else:
        return False,''




# # for reply count with date 

def get_date_wise_reply_counts(account_id,access_token):
    post_url = f"https://{INSTANCE}/api/v1/accounts/{account_id}/statuses"
    headers = {"Authorization": f"Bearer {access_token}"}
    params = {"limit": 100}
    reply_counts = defaultdict(int)

    while post_url:
        response = requests.get(post_url, headers=headers, params=params)
        response.raise_for_status()
        statuses = response.json()

        if not statuses:
            break

        for status in statuses:
            post_id = status['id']
            created_at = status['created_at']
            date = datetime.fromisoformat(created_at.rstrip("Z")).date()

            context_url = f"https://{INSTANCE}/api/v1/statuses/{post_id}/context"
            context_resp = requests.get(context_url, headers=headers)
            context_resp.raise_for_status()
            context = context_resp.json()
            replies = context.get("descendants", [])

            reply_counts[date] += len(replies)


        if 'next' in response.links:
            post_url = response.links['next']['url']
            params = None
        else:
            break

    if reply_counts:
        return True,reply_counts
    else:
        return False,''




# # for reblog count with date 

def get_date_wise_reblog_counts(account_id,access_token):
    url = f"https://{INSTANCE}/api/v1/accounts/{account_id}/statuses"
    headers = {"Authorization": f"Bearer {access_token}"}
    params = {"limit": 100}
    reblog_counts = defaultdict(int)

    while url:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        statuses = response.json()

        if not statuses:
            break

        for status in statuses:
            created_at = status['created_at']
            date = datetime.fromisoformat(created_at.rstrip("Z")).date()
            reblogs = status.get('reblogs_count')
            reblog_counts[date] += reblogs

        if 'next' in response.links:
            url = response.links['next']['url']
            params = None
        else:
            break

    if reblog_counts:
        return True,reblog_counts
    else:
        return False,''







# # for total metrics

def get_total_mastodon_metrics(account_id,access_token):
    user_url = f"https://{INSTANCE}/api/v1/accounts/verify_credentials"
    headers = {"Authorization": f"Bearer {access_token}"}
    user_response = requests.get(user_url, headers=headers)
    user_response.raise_for_status()
    user_data = user_response.json()

    total_followers = user_data.get('followers_count', 0)
    total_following = user_data.get('following_count', 0)
    total_posts = user_data.get('statuses_count', 0)

    status_url = f"https://{INSTANCE}/api/v1/accounts/{account_id}/statuses"
    params = {"limit": 100}
    total_likes = 0
    total_boosts = 0
    total_replies = 0

    while status_url:
        resp = requests.get(status_url, headers=headers, params=params)
        resp.raise_for_status()
        statuses = resp.json()

        if not statuses:
            break

        for post in statuses:
            total_likes += post.get('favourites_count', 0)
            total_boosts += post.get('reblogs_count', 0)

            context_url = f"https://{INSTANCE}/api/v1/statuses/{post['id']}/context"
            context_resp = requests.get(context_url, headers=headers)
            context_resp.raise_for_status()
            context = context_resp.json()
            replies = context.get("descendants", [])
            total_replies += len(replies)

        if 'next' in resp.links:
            status_url = resp.links['next']['url']
            params = None
        else:
            break

    if total_posts or total_likes or total_boosts or total_replies:
        return True,{
            "total_posts": total_posts,
            "followers": total_followers,
            "following": total_following,
            "likes": total_likes,
            "boosts": total_boosts,
            "replies": total_replies
        }
    else:
        return False,''


